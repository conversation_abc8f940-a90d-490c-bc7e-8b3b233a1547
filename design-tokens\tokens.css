/**
 * Material 3 Design Tokens v2
 * Generated from design-tokens-v2 JSON files
 * 
 * Usage:
 * - Light theme: Apply .light-theme class to body or root element
 * - Dark theme: Apply .dark-theme class to body or root element
 * - Reference colors are available globally as CSS custom properties
 */

/* ===== REFERENCE COLORS ===== */
:root {
  /* Primary Color Scale */
  --md-ref-palette-primary-50: #f1f5f9;
  --md-ref-palette-primary-100: #e9eff5;
  --md-ref-palette-primary-200: #cedde9;
  --md-ref-palette-primary-300: #a3c0d6;
  --md-ref-palette-primary-400: #729fbe;
  --md-ref-palette-primary-500: #5083a7;
  --md-ref-palette-primary-600: #3d698c;
  --md-ref-palette-primary-700: #325572;
  --md-ref-palette-primary-800: #2d495f;
  --md-ref-palette-primary-900: #293e51;
  --md-ref-palette-primary-950: #1b2836;

  /* Accent Color Scale */
  --md-ref-palette-accent-50: #f0fbfb;
  --md-ref-palette-accent-100: #d8f4f5;
  --md-ref-palette-accent-200: #b6e8eb;
  --md-ref-palette-accent-300: #83d6dd;
  --md-ref-palette-accent-400: #3bb4c1;
  --md-ref-palette-accent-500: #2d9fad;
  --md-ref-palette-accent-600: #288192;
  --md-ref-palette-accent-700: #276977;
  --md-ref-palette-accent-800: #275763;
  --md-ref-palette-accent-900: #254954;
  --md-ref-palette-accent-950: #132f39;

  /* Neutral Color Scale */
  --md-ref-palette-neutral-50: #f6f6f6;
  --md-ref-palette-neutral-100: #e7e7e7;
  --md-ref-palette-neutral-200: #d1d1d1;
  --md-ref-palette-neutral-300: #b0b0b0;
  --md-ref-palette-neutral-400: #888888;
  --md-ref-palette-neutral-500: #6d6d6d;
  --md-ref-palette-neutral-600: #5d5d5d;
  --md-ref-palette-neutral-700: #4f4f4f;
  --md-ref-palette-neutral-800: #454545;
  --md-ref-palette-neutral-900: #3d3d3d;
  --md-ref-palette-neutral-950: #0a0a0a;

  /* Error Color Scale */
  --md-ref-palette-error-50: #fff1f1;
  --md-ref-palette-error-100: #ffdfe0;
  --md-ref-palette-error-200: #ffc4c6;
  --md-ref-palette-error-300: #ff9b9e;
  --md-ref-palette-error-400: #ff6266;
  --md-ref-palette-error-500: #ff3137;
  --md-ref-palette-error-600: #f11c22;
  --md-ref-palette-error-700: #cb0a0f;
  --md-ref-palette-error-800: #a70d11;
  --md-ref-palette-error-900: #8a1215;
  --md-ref-palette-error-950: #4c0305;

  /* Base Colors */
  --md-ref-palette-white: #ffffff;
  --md-ref-palette-black: #000000;

  /* ===== TYPOGRAPHY TOKENS ===== */
  /* Desktop Font Sizes */
  --md-sys-typescale-headline-large-size: 3.562rem; /* 57px */
  --md-sys-typescale-headline-medium-size: 3rem; /* 48px */
  --md-sys-typescale-headline-small-size: 2.5rem; /* 40px */
  --md-sys-typescale-title-large-size: 2rem; /* 32px */
  --md-sys-typescale-title-medium-size: 1.75rem; /* 28px */
  --md-sys-typescale-title-small-size: 1.5rem; /* 24px */
  --md-sys-typescale-body-large-size: 1.25rem; /* 20px */
  --md-sys-typescale-body-medium-size: 1rem; /* 16px */
  --md-sys-typescale-body-small-size: 0.812rem; /* 13px */
  --md-sys-typescale-label-small-size: 0.688rem; /* 11px */
}

/* ===== LIGHT THEME SYSTEM COLORS ===== */
.light-theme,
:root {
  /* Base Surface Colors */
  --md-sys-color-surface: var(--md-ref-palette-primary-50);
  --md-sys-color-on-surface: var(--md-ref-palette-neutral-950);
  --md-sys-color-surface-gradient: #f1f5f900;
  --md-sys-color-outline: var(--md-ref-palette-neutral-200);
  --md-sys-color-surface-container: var(--md-ref-palette-white);
  --md-sys-color-surface-dim: #1b283640;
  --md-sys-color-on-surface-variant: var(--md-ref-palette-neutral-400);

  /* Primary Colors */
  --md-sys-color-primary: var(--md-ref-palette-primary-600);
  --md-sys-color-on-primary: var(--md-ref-palette-white);
  --md-sys-color-primary-container: var(--md-ref-palette-primary-600);

  /* Secondary Colors */
  --md-sys-color-secondary: var(--md-ref-palette-neutral-200);
  --md-sys-color-on-secondary: var(--md-ref-palette-neutral-950);
  --md-sys-color-on-secondary-variant: var(--md-ref-palette-white);

  /* Interactive States */
  --md-sys-color-hover: #6d6d6d40;

  /* Error Colors */
  --md-sys-color-error: var(--md-ref-palette-error-800);
  --md-sys-color-on-error: var(--md-ref-palette-white);
}

/* ===== DARK THEME SYSTEM COLORS ===== */
.dark-theme {
  /* Base Surface Colors */
  --md-sys-color-surface: var(--md-ref-palette-black);
  --md-sys-color-on-surface: var(--md-ref-palette-neutral-50);
  --md-sys-color-surface-gradient: #00000000;
  --md-sys-color-outline: var(--md-ref-palette-neutral-300);
  --md-sys-color-surface-container: var(--md-ref-palette-primary-950);
  --md-sys-color-surface-dim: #f1f5f940;
  --md-sys-color-on-surface-variant: var(--md-ref-palette-neutral-400);

  /* Primary Colors */
  --md-sys-color-primary: var(--md-ref-palette-primary-600);
  --md-sys-color-on-primary: var(--md-ref-palette-white);
  --md-sys-color-primary-container: var(--md-ref-palette-primary-600);

  /* Secondary Colors */
  --md-sys-color-secondary: var(--md-ref-palette-neutral-900);
  --md-sys-color-on-secondary: var(--md-ref-palette-white);
  --md-sys-color-on-secondary-variant: var(--md-ref-palette-neutral-400);

  /* Interactive States */
  --md-sys-color-hover: #6d6d6d40;

  /* Error Colors */
  --md-sys-color-error: var(--md-ref-palette-error-800);
  --md-sys-color-on-error: var(--md-ref-palette-white);
}

/* ===== UTILITY CLASSES ===== */
/* Quick theme switching utilities */
.theme-light {
  color-scheme: light;
}

.theme-dark {
  color-scheme: dark;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
/* Mobile font sizes for smaller screens */
@media (max-width: 768px) {
  :root {
    /* Mobile Font Sizes */
    --md-sys-typescale-headline-large-size: 3rem;
    --md-sys-typescale-headline-medium-size: 2.25rem;
    --md-sys-typescale-headline-small-size: 2rem;
    --md-sys-typescale-title-large-size: 1.75rem;
    --md-sys-typescale-title-medium-size: 1.5rem;
    --md-sys-typescale-title-small-size: 1.25rem;
    --md-sys-typescale-body-large-size: 1.25rem;
    --md-sys-typescale-body-medium-size: 1rem;
    --md-sys-typescale-body-small-size: 0.812rem;
    --md-sys-typescale-label-small-size: 0.688rem;
  }
}

/* ===== HELPER CLASSES ===== */
/* Typography helper classes */
.md-headline-large {
  font-size: var(--md-sys-typescale-headline-large-size); 
  font-weight: 700;
}

.md-headline-medium {
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: 700;
}

.md-headline-small {
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: 700;
}

.md-title-large {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: 700;
}

.md-title-medium {
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: 700;
}

.md-title-small {
  font-size: var(--md-sys-typescale-title-small-size); 
  font-weight: 700;
}

.md-body-large {
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: 400;
}

.md-body-medium {
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
}

.md-body-small {
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 400;
}

.md-label-small {
  font-size: var(--md-sys-typescale-label-small-size);
  font-weight: 400;
}

/* Color helper classes */
.md-surface {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
}

.md-primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.md-secondary {
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);
}

.md-error {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);
}

/* Text color utilities */
.text-primary {
  color: var(--md-sys-color-primary);
}

.text-on-surface {
  color: var(--md-sys-color-on-surface);
}

.text-on-surface-variant {
  color: var(--md-sys-color-on-surface-variant);
}

.text-error {
  color: var(--md-sys-color-error);
}
