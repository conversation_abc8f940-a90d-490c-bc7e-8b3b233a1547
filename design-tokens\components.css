/**
 * STT-SaaS 組件樣式檔案
 * 基於 Material 3 Design Tokens 重構
 * 版本: 2.0.0
 * 建立日期: 2025-01-09
 *
 * 依賴: tokens.css (Material 3 Design Tokens)
 * 支援: 明暗主題切換、響應式設計
 */

/* ========================================
   引入 Material 3 Design Tokens
   ======================================== */
@import url('./tokens.css');

/* ========================================
   全域基礎設定
   ======================================== */

/* 字體載入優化 */
@font-face {
  font-family: 'Noto Sans';
  src: url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap');
  font-display: swap;
}

/* 全域重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  /* 確保 1rem = 16px */
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Noto Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  background-color: var(--md-sys-color-surface);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* ========================================
   通用工具類別
   ======================================== */

/* 文字工具類別 */
.text-primary {
  color: var(--md-sys-color-primary);
}

.text-secondary {
  color: var(--md-sys-color-on-surface-variant);
}

.text-tertiary {
  color: var(--md-ref-palette-neutral-500);
}

.text-placeholder {
  color: var(--md-ref-palette-neutral-400);
}

/* 背景工具類別 */
.bg-primary {
  background-color: var(--md-sys-color-surface);
}

.bg-secondary {
  background-color: var(--md-sys-color-surface-container);
}

.bg-subtle {
  background-color: var(--md-ref-palette-neutral-50);
}

/* 邊框工具類別 */
.border-primary {
  border-color: var(--md-sys-color-outline);
}

.border-secondary {
  border-color: var(--md-ref-palette-neutral-200);
}

.border-accent {
  border-color: var(--md-sys-color-primary);
}

/* 字重工具類別 */
.font-thin {
  font-weight: 100;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

/* ========================================
   常用元件樣式
   ======================================== */

/* 按鈕基礎樣式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-height: 40px;
  padding: 7px 16px;
  border: none;
  border-radius: 60px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 按鈕變體 */
.btn-primary {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--md-ref-palette-primary-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--md-sys-color-surface-dim);
}

.btn-secondary {
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);
  padding: 11px;
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--md-ref-palette-neutral-300);
}

/* 按鈕尺寸 */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: var(--md-sys-typescale-body-small-size);
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: var(--md-sys-typescale-body-large-size);
}

/* ========================================
   AppBar 組件
   ======================================== */

.app-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 1rem;
  color: var(--md-sys-color-on-surface);
  /* 按要求不使用 background-color 和 border */
}

.app-bar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.app-bar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.app-bar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* ========================================
   ToolBar 組件
   ======================================== */

.tool-bar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  overflow-x: auto;
}

.tab-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 20px;
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  user-select: none;
}

.tab-item-active {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
}

.tab-item-inactive {
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);
}

.tab-item:hover:not(.tab-item-active) {
  background-color: var(--md-sys-color-hover);
}

.language-switcher {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: auto;
}

/* ========================================
   Chip 組件 (通用標籤) - 新版
   ======================================== */

.chip {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  max-height: 40px;
  background-color: var(--md-sys-color-secondary);
  color: var(--md-sys-color-on-secondary);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 20px;
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.chip:hover {
  background-color: var(--md-sys-color-hover);
  color: rgba(109, 109, 109, 0.25);
}

.chip-removable {
  padding-right: 0.5rem;
}

.chip-remove-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  background: none;
  border: none;
  border-radius: 50%;
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
}

.chip-remove-btn:hover {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);
}

/* ========================================
   Input 組件擴展 - 文字輸入框變體
   ======================================== */

.input-text {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 16px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
}

.input-text.error {
  border-color: var(--md-sys-color-error);
}

.input-text.input-doing,
.input-text:focus {
  outline: none;
  border-color: var(--md-sys-color-on-surface-variant);
}

/* ========================================
   Input 組件擴展 - 下拉清單變體
   ======================================== */

.input-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 16px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23888888' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.input-select.input-done {
  border-color: var(--md-sys-color-on-surface-variant);
}

.input-select.dropdownlist-hover,
.input-select:hover {
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-ref-palette-primary-50);
}

.input-select.input-doing,
.input-select:focus {
  outline: none;
  border-color: var(--md-sys-color-on-surface-variant);
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
}

.input-label.required::after {
  content: " *";
  color: var(--md-sys-color-error);
}

/* 輸入框樣式 */
.input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 4px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface);
}

.input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
}

/* 表單控制項樣式 (與 input 一致) */
.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 4px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface);
}

/* 搜尋輸入容器 */
.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  z-index: 1;
  color: var(--md-sys-color-on-surface-variant);
  pointer-events: none;
}

.search-select {
  padding-left: 2.5rem;
  /* 為搜尋圖示留出空間 */
  appearance: none;
  /* 移除預設下拉箭頭 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23888888' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* 選擇的語言標籤容器 */
.selected-languages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  color: var(--md-sys-color-on-surface);
  margin: 1rem 0;
}

/* 語言標籤樣式 */
.language-pill {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--md-sys-color-surface-container);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 20px;
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 400;
  color: var(--md-sys-color-on-surface);
  transition: all 0.2s ease;
}

.language-pill:hover {
  background-color: var(--md-ref-palette-primary-100);
  border-color: var(--md-sys-color-primary);
}

/* 移除按鈕樣式 */
.remove-pill {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  background: none;
  border: none;
  border-radius: 50%;
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
}

.remove-pill:hover {
  background-color: var(--md-sys-color-error);
  color: var(--md-sys-color-on-error);
}

/* 表單樣式 */
form .btn{
  height: 48px;
  width: 100%;
}

#login-form, #language-select-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
}

.form-group h2 {
  color: var(--md-sys-color-on-surface);
  margin: 0;
  font-size: var(--md-sys-typescale-title-medium-size);
  font-weight: 500;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 6px;
  background-color: var(--md-sys-color-surface-container);
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface);
}

.form-group input::placeholder {
  color: var(--md-sys-color-on-surface-variant);
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
}

/* 卡片樣式 */
.card {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: center;
  width: 100%;
  max-width: 400px;
  max-height: 600px;
  padding: 1rem;
  background-color: var(--md-sys-color-surface-container);
  border-radius: 16px;
  box-shadow: 0 4px 6px var(--md-sys-color-surface-dim);
  border: 1px solid var(--md-sys-color-outline);
  transition: box-shadow 0.2s ease, background-color 0.2s ease;
}

.card:hover {
  box-shadow: 0 8px 16px var(--md-sys-color-surface-dim);
}

.card-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
}

.card-title {
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: 500;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface);
  margin: 0;
  text-align: center;
}

.card-content {
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  color: var(--md-sys-color-on-surface-variant);
}

/* 連結樣式 */
.link {
  color: var(--md-sys-color-primary);
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  transition: color 0.2s ease;
}

.link:hover {
  color: var(--md-ref-palette-primary-700);
  text-decoration: underline;
}

/* 狀態訊息 */
.message {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: var(--md-sys-typescale-body-medium-size);
  font-weight: 400;
  line-height: 1.5;
  border: 1px solid transparent;
}

.message-success {
  background-color: var(--md-ref-palette-accent-100);
  color: var(--md-ref-palette-accent-800);
  border-color: var(--md-ref-palette-accent-300);
}

.message-warning {
  background-color: #fef3c7;
  color: #92400e;
  border-color: #fcd34d;
}

.message-error {
  background-color: var(--md-ref-palette-error-100);
  color: var(--md-sys-color-error);
  border-color: var(--md-ref-palette-error-300);
}

.message-info {
  background-color: var(--md-ref-palette-primary-100);
  color: var(--md-ref-palette-primary-800);
  border-color: var(--md-ref-palette-primary-300);
}

/* ========================================
   響應式工具
   ======================================== */

/* 隱藏/顯示工具 */
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* 響應式顯示 */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }

  .visible-mobile {
    display: block !important;
  }

  /* 新組件響應式支援 */
  .app-bar {
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem;
  }

  .app-bar-left,
  .app-bar-center,
  .app-bar-right {
    width: 100%;
    justify-content: center;
  }

  .tool-bar {
    padding: 0.25rem;
    gap: 0.25rem;
  }

  .tab-item {
    padding: 0.375rem 0.75rem;
    font-size: var(--md-sys-typescale-body-small-size);
  }

  .chip {
    font-size: var(--md-sys-typescale-body-small-size);
    padding: 0.375rem 0.625rem;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }

  .visible-desktop {
    display: block !important;
  }
}

/* ========================================
   主題切換增強
   ======================================== */

/* 主題切換按鈕樣式 */
.theme-toggle-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 12px;
  border: none;
  border-radius: 50%;
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px var(--md-sys-color-surface-dim);
}

.theme-toggle-btn:hover {
  background-color: var(--md-ref-palette-primary-700);
  transform: scale(1.1);
}

/* 暗色主題特定樣式調整 */
.dark-theme .card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.dark-theme .card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

.dark-theme .btn-primary:hover:not(:disabled) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* ========================================
   可訪問性增強
   ======================================== */

/* 焦點樣式 */
*:focus {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* ========================================
   QR Code Card 組件樣式
   基於 Material 3 設計系統和圖片標記間距規則
   ======================================== */

/* QR Code 卡片遮罩層 */
.qr-code-card-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem; /* 1rem 外邊距，對應圖片標記 "1" */
  box-sizing: border-box;
}

/* QR Code 卡片主體 */
.qr-code-card {
  background-color: var(--md-sys-color-surface-container);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: qrCardSlideIn 0.3s ease-out;
}

/* 卡片標題區域 */
.qr-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem 0 1rem; /* 頂部 1rem，左右 1rem，對應圖片標記 "1" */
  margin-bottom: 1rem; /* 1rem 下邊距，對應圖片標記 "1" */
}

.qr-card-title {
  font-family: var(--md-sys-typescale-title-medium-family, 'Noto Sans', 'Segoe UI', sans-serif);
  font-size: var(--md-title-small-size, var(--md-sys-typescale-title-small-size));
  font-weight: 700;
  color: var(--md-sys-color-on-surface);
  margin: auto;
}

.qr-card-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem; /* 2rem 寬度，對應圖片標記 "2" 的比例 */
  height: 2rem; /* 2rem 高度，對應圖片標記 "2" 的比例 */
  border: none;
  border-radius: 50%;
  background-color: transparent;
  color: var(--md-sys-color-on-surface-variant);
  cursor: pointer;
  transition: all 0.2s ease;
}

.qr-card-close-btn:hover {
  background-color: var(--md-sys-color-hover);
  color: var(--md-sys-color-on-surface);
}

.qr-card-close-btn:focus {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* QR Code 內容區域 */
.qr-card-content {
  padding: 0 2rem 2rem 2rem; /* 左右 2rem，底部 2rem，對應圖片標記 "2" */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem; /* 2rem 間距，對應圖片標記 "2" */
}

/* QR Code 圖片容器 */
.qr-code-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 4px solid var(--md-sys-color-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--md-sys-color-surface);
  position: relative;
}

.qr-code-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.qr-placeholder-icon {
  font-size: 4rem;
  color: var(--md-sys-color-on-surface-variant);
}

/* SCAN ME 按鈕容器 */
.scan-button-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.scan-me-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem; /* 1rem 上下，2rem 左右，對應圖片標記 */
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  border: none;
  border-radius: 60px;
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
}

.scan-me-btn:hover {
  background-color: var(--md-ref-palette-primary-700);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.scan-me-btn:focus {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

.scan-icon {
  font-size: 1.25rem;
}

.scan-text {
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 網址顯示區域 */
.url-display-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.url-display {
  background-color: var(--md-sys-color-surface);
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 8px;
  padding: 1rem; /* 1rem 內邊距，對應圖片標記 "1" */
  width: 100%;
  max-width: 300px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.url-text {
  font-family: 'Noto Sans', 'Segoe UI', sans-serif;
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface);
  word-break: break-all;
  line-height: 1.4;
}

/* QR Code 卡片動畫 */
@keyframes qrCardSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* QR Code 卡片響應式設計 */
@media (min-width: 744px) {
  .qr-code-card {
    max-width: 400px;
  }

  .qr-code-image {
    width: 200px;
    height: 200px;
  }
}

@media (min-width: 320px) and (max-width: 600px) {
  .qr-code-card-overlay {
    padding: 0.5rem; /* 手機上減少外邊距 */
  }

  .qr-code-card {
    max-width: 100%;
    margin: 0;
  }

  .qr-card-header {
    padding: 0.75rem 1rem 0 1rem;
    margin-bottom: 0.75rem;
  }

  .qr-card-title {
    font-size: var(--md-sys-typescale-title-small-size);
  }

  .qr-card-content {
    padding: 0 1rem 1.5rem 1rem; /* 手機上減少內邊距 */
    gap: 1.5rem; /* 手機上減少間距 */
  }

  .qr-code-image {
    width: 160px;
    height: 160px;
  }

  .qr-placeholder-icon {
    font-size: 3rem;
  }

  .scan-me-btn {
    padding: 0.75rem 1.5rem;
    font-size: var(--md-sys-typescale-body-medium-size);
    min-width: 140px;
  }

  .scan-icon {
    font-size: 1.1rem;
  }

  .url-display {
    padding: 0.75rem;
    max-width: 100%;
  }

  .url-text {
    font-size: var(--md-sys-typescale-body-small-size);
  }
}

@media (min-width: 1200px) {
  .qr-code-card {
    max-width: 450px;
  }

  .qr-code-image {
    width: 220px;
    height: 220px;
  }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}